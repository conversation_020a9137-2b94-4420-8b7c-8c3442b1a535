#!/usr/bin/env python3
"""
TCP String Search Server

A high-performance TCP server that searches for exact string matches in files.
Supports SSL authentication, multithreading, and configurable file re-reading.

Author: Interview Candidate
Date: 2024
"""

import configparser
import logging
import os
import socket
import ssl
import time
from socketserver import BaseRequestHandler, ThreadingTCPServer
from typing import Any, Optional, Tuple, cast


class ConfigurationError(Exception):
    """Raised when configuration file has invalid or missing parameters."""

    pass


class FileSearchError(Exception):
    """Raised when file search operations fail."""

    pass


class ServerConfig:
    """ServerConfig manages the loading, parsing, and validation of configuration
    parameters for the TCP String Search Server from a config.ini file.

    Features:
    - Loads configuration from a specified file.
    - Validates the presence and type of required parameters: linuxpath,
      REREAD_ON_QUERY, host, port, max_connections.
    - Ensures the file specified by 'linuxpath' exists and is readable.
    - Provides methods to retrieve configuration values as string, boolean,
      or integer, with support for default values and section overrides.

    Raises:
        ConfigurationError: If the configuration file is missing, invalid,
        or required parameters are absent or of incorrect type.
    """

    def __init__(self, config_file: str = "config.ini") -> None:
        """
        Initialize configuration from file.

        Args:
            config_file: Path to configuration file

        Raises:
            ConfigurationError: If config file is invalid or missing required parameters
        """
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        # Set option transform to lowercase for case-insensitive keys
        # Using setattr to avoid mypy method assignment error

        def _optionxform(optionstr: str) -> str:
            return str(optionstr).lower()

        setattr(self.config, 'optionxform', _optionxform)
        self._load_config()
        self._validate_config()

    def _load_config(self) -> None:
        """Load configuration from file."""
        if not os.path.exists(self.config_file):
            raise ConfigurationError(f"Configuration file {self.config_file} not found")

        try:
            self.config.read(self.config_file)
        except Exception as e:
            raise ConfigurationError(f"Failed to parse configuration file: {e}")

    def _validate_config(self) -> None:
        """Validate required configuration parameters."""
        required_params: dict[str, type] = {
            "linuxpath": str,
            "reread_on_query": bool,
            "host": str,
            "port": int,
            "max_connections": int,
        }

        # First check DEFAULT section for required parameters
        for param, param_type in required_params.items():
            value = None
            # Check in all sections including DEFAULT
            for section in [None] + self.config.sections():
                try:
                    if section:
                        value = self.config[section].get(param)
                    else:
                        value = self.config.defaults().get(param)
                    if value is not None:
                        break
                except Exception:
                    continue

            if value is None:
                raise ConfigurationError(
                    f"Required parameter '{param}' not found in any section"
                )

            # Validate parameter type
            try:
                if param_type == bool:
                    self.getboolean(param)
                elif param_type == int:
                    self.getint(param)
            except ValueError:
                raise ConfigurationError(
                    f"Invalid type for parameter '{param}', "
                    f"expected {param_type.__name__}"
                )

        # Store validated linuxpath
        self.file_path = self.get("linuxpath")

        # Validate file exists and is readable
        if not os.path.exists(self.file_path):
            raise ConfigurationError(f"File not found: {self.file_path}")
        if not os.access(self.file_path, os.R_OK):
            raise ConfigurationError(f"File not readable: {self.file_path}")

    def get(self, key: str, default: Any = None, section: Optional[str] = None) -> Any:
        """
        Get configuration value.

        Args:
            key: Configuration key
            default: Default value if key not found
            section: Configuration section (if None, searches all sections)

        Returns:
            Configuration value
        """
        if section:
            try:
                return self.config.get(section, key)
            except (configparser.NoSectionError, configparser.NoOptionError):
                return default

        # Try DEFAULT section first
        value = self.config.defaults().get(key)
        if value is not None:
            return value

        # Then try all other sections
        for sec in self.config.sections():
            try:
                value = self.config.get(sec, key)
                return value
            except configparser.NoOptionError:
                continue

        return default

    def getboolean(
        self, key: str, default: bool = False, section: str = "DEFAULT"
    ) -> bool:
        """Get boolean configuration value."""
        try:
            return self.config.getboolean(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return default

    def getint(self, key: str, default: int = 0, section: str = "DEFAULT") -> int:
        """Get integer configuration value."""
        try:
            return self.config.getint(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return default


class FileSearchEngine:
    """
    High-performance file search engine for exact string matching.

    Uses Hash Set search algorithm for optimal performance, with configurable
    reread_on_query.
    """

    def __init__(self, file_path: str, reread_on_query: bool = False) -> None:
        """
        Initialize file search engine with Hash Set algorithm only.

        Args:
            file_path: Path to the file to search
            reread_on_query: Whether to re-read file on each query

        Raises:
            FileSearchError: If file cannot be accessed
        """
        self.file_path = file_path
        self.reread_on_query = reread_on_query
        self._validate_file()
        # Use the optimized HashSetSearch with correct reread_on_query
        from search_algorithms_new import HashSetSearch

        self.searcher = HashSetSearch(
            use_frozenset=True, reread_on_query=self.reread_on_query)
        # Load the file into the searcher if not reread mode
        if not self.reread_on_query:
            self.searcher.load(file_path)

    def _validate_file(self) -> None:
        """Validate that the file exists and is readable."""
        if not os.path.exists(self.file_path):
            raise FileSearchError(f"File not found: {self.file_path}")

        if not os.access(self.file_path, os.R_OK):
            raise FileSearchError(f"File not readable: {self.file_path}")

    def search(self, query: str) -> bool:
        """
        Search for exact string match in file.

        Args:
            query: String to search for (exact line match)

        Returns:
            True if string found, False otherwise

        Raises:
            FileSearchError: If file operation fails
        """
        try:
            if self.reread_on_query:
                # Always pass file_path for reread mode
                return self.searcher.search(query, self.file_path)
            else:
                return self.searcher.search(query)
        except Exception as e:
            raise FileSearchError(f"Search failed: {e}")


def setup_logging(log_level: str = "DEBUG", log_file: str = "logs/server.log") -> None:
    """
    Set up logging configuration.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_file: Path to log file
    """
    if not log_file:
        log_file = "logs/server.log"

    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(log_file)
    if log_dir:
        os.makedirs(log_dir, exist_ok=True)

    # Configure logging format
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # Set up logging to both file and console
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[logging.FileHandler(log_file), logging.StreamHandler()],
    )


class StringSearchHandler(BaseRequestHandler):
    """
    TCP request handler for string search operations.

    Optimized for high-performance string search with minimal overhead.
    Uses direct buffer access and minimal logging for maximum throughput.
    """

    def handle(self) -> None:
        """
        Handle incoming TCP connection and process search request.

        Protocol:
        1. Receive up to 1024 bytes from client
        2. Strip null characters from end
        3. Search for exact string match in configured file
        4. Return "STRING EXISTS" or "STRING NOT FOUND" with newline
        5. Log debug information including timing and client IP
        """
        client_ip = self.client_address[0]

        try:
            # Get max payload size from config, default to 1024
            # Access config from the server instance, with fallback for static analysis
            server_config = getattr(self.server, "config", None)
            if server_config is not None:
                max_payload: int = server_config.getint(
                    "max_payload_size", 1024, section="server"
                )
            else:
                max_payload = 1024  # Fallback default

            # Receive data with maximum payload size limit
            data = self.request.recv(max_payload)

            if not data:
                logging.warning(f"Empty request from {client_ip}")
                return

            # Strip null characters from end as specified
            query = data.rstrip(b"\x00").decode("utf-8", errors="ignore").strip()

            if not query:
                logging.warning(f"Empty query after processing from {client_ip}")
                self.request.sendall(b"STRING NOT FOUND\n")
                return

            # Only measure the search operation time
            start_time = time.time()
            found: bool = cast("StringSearchServer", self.server).search_engine.search(
                query
            )
            search_time = (time.time() - start_time) * 1000

            # Send response
            response = "STRING EXISTS\n" if found else "STRING NOT FOUND\n"
            self.request.sendall(response.encode("utf-8"))

            # Log the actual search time and request details
            logging.debug(
                f"Query='{query}', IP={client_ip}, "
                f"Result={'FOUND' if found else 'NOT_FOUND'}, "
                f"SearchTime={search_time:.2f}ms, "
                f"Timestamp={time.strftime('%Y-%m-%d %H:%M:%S')}"
            )

        except UnicodeDecodeError as e:
            logging.error(f"Unicode decode error from {client_ip}: {e}")
            self.request.sendall(b"STRING NOT FOUND\n")
        except Exception as e:
            logging.error(
                f"Error processing request from {client_ip}: {e}"
            )
            self.request.sendall(b"STRING NOT FOUND\n")
        finally:
            # Ensure connection is closed properly
            try:
                self.request.shutdown(socket.SHUT_RDWR)
            except Exception:
                pass  # Socket might already be closed


class StringSearchServer(ThreadingTCPServer):
    """
    Multithreaded TCP server for string search operations.

    Extends ThreadingTCPServer to handle multiple concurrent connections
    while maintaining shared configuration and search engine instances.
    """

    # Allow socket reuse to avoid "Address already in use" errors
    allow_reuse_address = True
    # Reduce wait time for socket reuse
    request_queue_size = 50  # Handle more pending connections

    def __init__(
        self,
        server_address: Tuple[str, int],
        config: ServerConfig,
        search_engine: FileSearchEngine,
    ) -> None:
        """
        Initialize the string search server.

        Args:
            server_address: (host, port) tuple for server binding
            config: Server configuration instance
            search_engine: File search engine instance
        """
        self.config = config
        self.search_engine = search_engine

        # Initialize the threading TCP server
        super().__init__(server_address, StringSearchHandler)

        # Configure socket options for better performance
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

        # Set socket buffer sizes for better performance
        buffer_size = self.config.getint(
            "socket_buffer_size", 262144)  # 256KB buffer default
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, buffer_size)
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, buffer_size)

        # Enable TCP_NODELAY for lower latency
        if self.config.getboolean("tcp_nodelay", True):
            self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)

        # Set keep-alive for persistent connections
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)

        # Set socket timeouts
        timeout = self.config.getint("connection_timeout", 5)  # 5 seconds default
        self.socket.settimeout(timeout)

        logging.info(f"Server initialized on {server_address[0]}:{server_address[1]}")
        logging.info(f"File path: {search_engine.file_path}")
        logging.info(f"REREAD_ON_QUERY: {search_engine.reread_on_query}")

    def server_bind(self) -> None:
        """Override server_bind to properly handle IPv6."""
        address = self.server_address[0]
        if isinstance(address, str) and ":" in address:
            self.socket.setsockopt(socket.IPPROTO_IPV6, socket.IPV6_V6ONLY, 0)
        elif isinstance(address, (bytes, bytearray)) and b":" in address:
            self.socket.setsockopt(socket.IPPROTO_IPV6, socket.IPV6_V6ONLY, 0)
        super().server_bind()

    def handle_error(self, request: Any, client_address: Tuple[str, int]) -> None:
        """Handle an error gracefully."""
        _ = request  # Unused but required by interface
        logging.error(
            f"Error processing request from {client_address[0]}", exc_info=True
        )

    def shutdown_request(self, request: Any) -> None:
        """Clean up request (close socket)."""
        try:
            request.shutdown(socket.SHUT_RDWR)
        except Exception:
            pass  # Socket might already be closed
        super().shutdown_request(request)


def create_ssl_context(config: ServerConfig) -> Optional[ssl.SSLContext]:
    """
    Create SSL context for secure connections if SSL is enabled.

    Args:
        config: Server configuration

    Returns:
        SSL context if SSL enabled, None otherwise

    Raises:
        FileSearchError: If SSL certificates are missing or invalid
    """
    if not config.getboolean("ssl_enabled", False, section="ssl"):
        return None

    cert_file = config.get("ssl_cert_file", "certs/server.crt", section="ssl")
    key_file = config.get("ssl_key_file", "certs/server.key", section="ssl")
    verify_mode = config.getboolean("verify_client_cert", False, section="ssl")
    min_tls_version = config.get("min_tls_version", "TLSv1.3", section="ssl")

    if not os.path.exists(cert_file):
        raise FileSearchError(f"SSL certificate file not found: {cert_file}")

    if not os.path.exists(key_file):
        raise FileSearchError(f"SSL key file not found: {key_file}")

    try:
        # Create SSL context with modern security settings
        context = ssl.create_default_context(
            ssl.Purpose.CLIENT_AUTH if verify_mode else ssl.Purpose.CLIENT_AUTH
        )

        # Set minimum TLS version
        if min_tls_version == "TLSv1.3":
            context.minimum_version = ssl.TLSVersion.TLSv1_3
        elif min_tls_version == "TLSv1.2":
            context.minimum_version = ssl.TLSVersion.TLSv1_2
        else:
            logging.warning(
                f"Unsupported TLS version {min_tls_version}, defaulting to TLSv1.3")
            context.minimum_version = ssl.TLSVersion.TLSv1_3

        # Load certificate chain
        context.load_cert_chain(cert_file, key_file)

        # Set secure cipher suites
        context.set_ciphers('ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384')

        # Additional security options
        context.options |= (
            ssl.OP_NO_SSLv2 |  # Disable SSLv2
            ssl.OP_NO_SSLv3 |  # Disable SSLv3
            ssl.OP_NO_TLSv1 |  # Disable TLSv1.0
            ssl.OP_NO_TLSv1_1  # Disable TLSv1.1
        )

        # Enable client certificate verification if configured
        if verify_mode:
            context.verify_mode = ssl.CERT_REQUIRED
            ca_file = config.get("ca_file", None, section="ssl")
            if ca_file and os.path.exists(ca_file):
                context.load_verify_locations(ca_file)
            else:
                logging.warning(
                    "Client certificate verification enabled but no CA file "
                    "specified or found")

        logging.info(f"SSL context created successfully with {min_tls_version}")
        return context
    except Exception as e:
        raise FileSearchError(f"Failed to create SSL context: {e}")


def main() -> None:
    import argparse

    # Add command-line argument parsing
    parser = argparse.ArgumentParser(description="TCP String Search Server")
    parser.add_argument('--reread', action='store_true',
                        help="Enable REREAD_ON_QUERY mode")
    args = parser.parse_args()

    server = None
    try:
        # Set up logging first
        setup_logging("DEBUG", "logs/server.log")
        logging.info("Starting TCP String Search Server")

        # Load configuration
        config = ServerConfig()
        logging.info(f"Configuration loaded from: {config.config_file}")

        # Override REREAD_ON_QUERY if command-line argument is provided
        if args.reread:
            config.config["DEFAULT"]["reread_on_query"] = "True"
            logging.info("REREAD_ON_QUERY mode enabled via command-line argument")

        # Initialize file search engine
        reread_on_query = config.getboolean("reread_on_query", False)
        search_engine = FileSearchEngine(config.file_path, reread_on_query)

        # Get server configuration
        host = config.get("host", "localhost")
        port = config.getint("port", 8888)

        # Create and start server
        server = StringSearchServer((host, port), config, search_engine)

        # Set up SSL if enabled
        ssl_context = create_ssl_context(config)
        if ssl_context:
            server.socket = ssl_context.wrap_socket(server.socket, server_side=True)
            logging.info("SSL enabled for server connections")

        logging.info(f"Server listening on {host}:{port}")
        logging.info("Press Ctrl+C to stop the server")

        # Start serving requests
        server.serve_forever()

    except KeyboardInterrupt:
        logging.info("Server shutdown requested by user")
    except Exception as e:
        logging.error(f"Server startup failed: {e}")
    finally:
        if server is not None:
            server.shutdown()
            server.server_close()
            logging.info("Server stopped")


if __name__ == "__main__":
    main()
