#!/usr/bin/env python3
"""Tests for FileSearchEngine class."""

import os
import pytest
import tempfile
from server import FileSearchEngine, FileSearchError

@pytest.fixture
def test_file():
    """Create a temporary test file."""
    with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
        f.write("line one\n")
        f.write("line two\n")
        f.write("test line\n")
        f.write("final line\n")
    yield f.name
    os.unlink(f.name)

def test_file_not_found():
    """Test handling of non-existent file."""
    with pytest.raises(FileSearchError):
        FileSearchEngine("nonexistent.txt")

def test_search_cached(test_file: str):
    """Test cached search mode."""
    engine = FileSearchEngine(test_file, reread_on_query=False)
    assert engine.search("line one") is True
    assert engine.search("line two") is True
    assert engine.search("nonexistent line") is False

def test_search_reread(test_file: str):
    """Test re-read search mode."""
    engine = FileSearchEngine(test_file, reread_on_query=True)
    assert engine.search("line one") is True
    assert engine.search("line two") is True
    assert engine.search("nonexistent line") is False

def test_file_modification(test_file: str):
    """Test file modification handling."""
    engine = FileSearchEngine(test_file, reread_on_query=True)
    assert engine.search("new line") is False
    
    # Add new line to file
    with open(test_file, 'a') as f:
        f.write("new line\n")
    
    # Should find the new line when re-reading
    assert engine.search("new line") is True
    
    # Cached version should not find the new line
    cached_engine = FileSearchEngine(test_file, reread_on_query=False)
    cached_engine.search("line one")  # Initial cache load
    
    # Add another line
    with open(test_file, 'a') as f:
        f.write("another new line\n")
    
    # Cached version should not see the new line
    assert cached_engine.search("another new line") is False

def test_empty_file(test_file: str):
    """Test handling of empty file."""
    # Create empty file
    with open(test_file, 'w') as _:
        pass
    
    engine = FileSearchEngine(test_file)
    assert engine.search("any string") is False

def test_large_file():
    """Test search in larger file."""
    file_path = "test_data/200k.txt"
    engine = FileSearchEngine(file_path)
    
    # Test first line
    with open(file_path, 'r') as f:
        first_line = f.readline().strip()
    assert engine.search(first_line) is True
    
    # Test random string that shouldn't exist
    assert engine.search("THIS STRING SHOULD NOT EXIST IN FILE 123!@#") is False

def test_unicode_handling(test_file: str):
    """Test handling of Unicode content."""
    test_strings = [
        "Hello, 世界!",
        "Café", 
        "über",
        "ñandú"
    ]
    
    # Write Unicode content
    with open(test_file, 'w', encoding='utf-8') as f:
        for s in test_strings:
            f.write(f"{s}\n")
    
    engine = FileSearchEngine(test_file)
    for s in test_strings:
        assert engine.search(s) is True