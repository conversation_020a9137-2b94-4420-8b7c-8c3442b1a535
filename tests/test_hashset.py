import time
from search_algorithms_new import HashSetSearch

def test_search_speed():
    test_files = [
        ("test_data/bench_10000.txt", "10K"),
        ("test_data/bench_50000.txt", "50K"),
        ("test_data/bench_100000.txt", "100K"),
        ("test_data/bench_250000.txt", "250K"),
    ]
    
    n_searches = 10
    test_words = ["test", "example", "benchmark", "13;0;1;26;0;7;3;0;", "speed"]
    
    for file_path, size_label in test_files:
        searcher = HashSetSearch(use_frozenset=True, reread_on_query=True)
        print(f"\nTesting HashSetSearch with file size {size_label}")
        print("-" * 50)
        
        total_time = 0
        found_count = 0
        
        # Test with different words for more realistic results
        for i in range(n_searches):
            word = test_words[i % len(test_words)]
            start = time.perf_counter()
            found = searcher.search(word, file_path)
            duration = (time.perf_counter() - start) * 1000
            if found:
                found_count += 1
            total_time += duration
            print(f"Search {i+1} ({word:12}): {duration:6.2f}ms {'[FOUND]' if found else ''}")
        
        avg_time = total_time / n_searches
        print("-" * 50)
        print(f"File size: {size_label}")
        print(f"Average search time: {avg_time:.2f}ms")
        print(f"Words found: {found_count}/{n_searches}")
        print("-" * 50)

if __name__ == "__main__":
    test_search_speed() 