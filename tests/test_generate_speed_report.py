#!/usr/bin/env python3
"""
Test suite for generate_speed_report.py
"""
import pytest
import os
import tempfile
import shutil
import pandas as pd
from unittest.mock import patch, mock_open, MagicMock, Mock
from typing import List, Dict, Any
from search_algorithms_new import SearchAlgorithm, HashSetSearch

# Import from your module
from generate_speed_report import (
    benchmark_algorithm,
    create_test_queries,
    plot_comparison,
    generate_report
)

@pytest.fixture
def temp_test_file():
    """Create a temporary file with sample data for testing."""
    lines = [
        "apple\n", "banana\n", "cherry\n",
        "durian\n", "elderberry\n", "fig\n",
        "grape\n"
    ]
    with tempfile.NamedTemporaryFile(mode='w+', delete=False, suffix='.txt') as f:
        f.writelines(lines)
        test_path = f.name
    yield test_path
    os.unlink(test_path)


def test_create_test_queries(temp_test_file):
    """Test query generation includes both real and fake entries."""
    queries = create_test_queries(temp_test_file)
    assert isinstance(queries, list)
    assert len(queries) >= 2
    real_found = any(q in ["apple", "banana", "cherry", "durian", "elderberry", "fig", "grape"] for q in queries)
    fake_found = any("nonexistent" in q or "not_be_found" in q for q in queries)
    assert real_found or fake_found


def test_benchmark_algorithm(temp_test_file):
    """Test benchmark_algorithm returns correct structure."""
    algo = HashSetSearch()
    queries = ["apple", "banana", "not_found"]
    result = benchmark_algorithm(algo, temp_test_file, queries, iterations=2)
    assert isinstance(result, dict)
    assert 'Algorithm' in result
    assert 'Load Time (ms)' in result
    assert 'Avg Search Time (ms)' in result


@patch("matplotlib.pyplot.savefig")
@patch("matplotlib.pyplot.plot")
@patch("matplotlib.pyplot.close")
def test_plot_comparison(mock_close, mock_plot, mock_savefig):
    """Test that plots are created without crashing."""
    df = pd.DataFrame([
        {"Algorithm": "HashSet", "Size Label": "10K", "Load Time (ms)": 12.1, "Avg Search Time (ms)": 0.1},
        {"Algorithm": "Linear", "Size Label": "10K", "Load Time (ms)": 25.0, "Avg Search Time (ms)": 5.3}
    ])
    plot_comparison(df)
    assert mock_savefig.called
    assert mock_plot.called
    assert mock_close.called


@patch("builtins.open", new_callable=mock_open)
@patch("pandas.DataFrame.to_csv")
@patch("os.makedirs")
@patch("os.path.exists")
@patch("generate_speed_report.plot_comparison")
@patch("generate_speed_report.benchmark_algorithm")
@patch("generate_speed_report.create_test_queries")
def test_generate_report(
    mock_create_queries,
    mock_benchmark,
    mock_plot,
    mock_exists,
    mock_makedirs,
    mock_to_csv,
    mock_file
):
    """End-to-end smoke test of report generation logic with mocks."""
    mock_create_queries.return_value = ["apple", "banana", "not_found"]
    
    def benchmark_side_effect(algo: SearchAlgorithm, file_path: str, queries: List[str], iterations: int = 3) -> Dict[str, Any]:
        return {
            "Algorithm": algo.name(),
            "Load Time (ms)": 10.0,
            "Min Search Time (ms)": 0.1,
            "Max Search Time (ms)": 1.0,
            "Avg Search Time (ms)": 0.5,
            "Size Label": os.path.basename(file_path).split('_')[1].replace('.txt', '') + "K",
            "File Path": file_path
        }
    mock_benchmark.side_effect = benchmark_side_effect

    # Create test files in a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        test_files = [
            "bench_10000.txt",
            "bench_50000.txt",
            "bench_100000.txt",
        ]
        for filename in test_files:
            file_path = os.path.join(temp_dir, filename)
            with open(file_path, "w") as f:
                f.write("apple\nbanana\ncherry\n")

        # Create test files configuration for our temporary directory
        temp_test_files = [(os.path.join(temp_dir, f), f.split('_')[1].replace('.txt', 'K'))
                          for f in test_files]

        # Mock os.path.exists to return True for our test files
        mock_exists.return_value = True

        # Call generate_report with our temporary test files
        generate_report(test_files=temp_test_files)
        assert mock_plot.called
        assert mock_to_csv.called
        assert mock_file.called

        # Verify that the mock_benchmark was called with different algorithms
        assert mock_benchmark.call_count > 1
        # Check if different file sizes were processed
        size_labels = set()
        for call in mock_benchmark.mock_calls:
            file_path = call[1][1]  # Second argument of each call
            size = os.path.basename(file_path).split('_')[1].replace('.txt', '') + "K"
            size_labels.add(size)
        assert len(size_labels) > 1, "Should have processed multiple file sizes"


def test_create_test_queries_with_empty_file():
    """create_test_queries should handle empty files gracefully."""
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
        path = f.name
    try:
        queries = create_test_queries(path)
        assert isinstance(queries, list)
        assert len(queries) == 2  # should still include 2 non-existent queries
    finally:
        os.unlink(path)


@patch("generate_speed_report.benchmark_algorithm")
@patch("generate_speed_report.create_test_queries")
@patch("generate_speed_report.plot_comparison")
@patch("pandas.DataFrame.to_csv")
@patch("os.path.exists")
def test_generate_report_skips_missing_files(
    mock_exists, mock_to_csv, mock_plot, mock_create_queries, mock_benchmark
):
    """Test that generate_report skips files that do not exist."""
    mock_create_queries.return_value = ["fake"]

    # Mock os.path.exists to return False for all test files
    mock_exists.return_value = False

    generate_report()
    assert mock_benchmark.called is False  # Should not call benchmark when files don't exist
    assert mock_plot.called is False  # Should not try to plot when there's no data
    assert mock_to_csv.called is False  # Should not create CSV when there's no data

@patch("generate_speed_report.HashSetSearch")
def test_benchmark_algorithm_reread_mode(mock_hash_set_search):
    """Test benchmark_algorithm with reread_on_query mode."""
    # Create a mock algorithm with reread_on_query=True
    mock_algo = Mock()
    mock_algo.name.return_value = "Test Algorithm"
    mock_algo.reread_on_query = True
    mock_algo.search.return_value = True
    mock_algo.load.return_value = None

    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
        f.write("test_line\n")
        file_path = f.name

    try:
        result = benchmark_algorithm(mock_algo, file_path, ["test_line"])

        # Verify that search was called with file_path parameter
        mock_algo.search.assert_called_with("test_line", file_path=file_path)
        assert result['Algorithm'] == "Test Algorithm"
        assert 'Load Time (ms)' in result
        assert 'Avg Search Time (ms)' in result
    finally:
        os.unlink(file_path)

@patch("matplotlib.pyplot.plot")
@patch("matplotlib.pyplot.savefig")
@patch("matplotlib.pyplot.close")
def test_plot_comparison_empty_data(mock_close, mock_savefig, mock_plot):
    """Test plot_comparison with empty DataFrame."""
    import pandas as pd

    empty_df = pd.DataFrame()
    plot_comparison(empty_df)

    # Should return early and not create plots when data is empty
    assert not mock_plot.called
    assert not mock_savefig.called
    assert not mock_close.called

@patch("matplotlib.pyplot.plot")
@patch("matplotlib.pyplot.savefig")
@patch("matplotlib.pyplot.close")
def test_plot_comparison_no_algorithms(mock_close, mock_savefig, mock_plot):
    """Test plot_comparison with DataFrame that has no algorithms."""
    import pandas as pd

    # Create DataFrame with no algorithms
    df = pd.DataFrame({
        'Algorithm': [],
        'Size Label': [],
        'Load Time (ms)': [],
        'Avg Search Time (ms)': []
    })

    plot_comparison(df)

    # Should return early and not create plots when data is empty
    assert not mock_plot.called
    assert not mock_savefig.called
    assert not mock_close.called

def test_main_execution():
    """Test the main execution block."""
    # Test that the main execution block exists and can be called
    import generate_speed_report

    # Verify the module has the expected functions
    assert hasattr(generate_speed_report, 'generate_report')
    assert callable(generate_speed_report.generate_report)

    # Test the main block by patching and calling directly
    with patch('generate_speed_report.generate_report') as mock_generate:
        with patch('logging.info') as mock_logging:
            # Simulate the main block execution
            mock_logging("Generating speed report...")
            mock_generate()
            mock_logging("Speed report generation complete. Check docs/ folder for results.")

            # Verify calls were made
            assert mock_generate.called
            assert mock_logging.called


def test_plot_comparison_with_empty_dataframe():
    """plot_comparison should not crash on empty DataFrame."""
    df = pd.DataFrame(columns=["Algorithm", "Size Label", "Load Time (ms)", "Avg Search Time (ms)"])
    try:
        plot_comparison(df)  # Should not raise
    except Exception as e:
        pytest.fail(f"plot_comparison() raised exception on empty DataFrame: {e}")


def test_benchmark_algorithm_average_calculation(temp_test_file):
    """Verify that Avg Search Time is correct."""
    class FakeAlgo:
        def __init__(self):
            self._search_count = 0
        def load(self, file_path): pass
        def search(self, query): self._search_count += 1; return True
        def name(self): return "FakeAlgo"

    algo = FakeAlgo()
    queries = ["a", "b"]
    result = benchmark_algorithm(algo, temp_test_file, queries, iterations=2)
    assert result["Avg Search Time (ms)"] > 0
    assert result["Min Search Time (ms)"] <= result["Max Search Time (ms)"]
    assert result["Algorithm"] == "FakeAlgo"
    assert algo._search_count == 4  # 2 queries × 2 iterations