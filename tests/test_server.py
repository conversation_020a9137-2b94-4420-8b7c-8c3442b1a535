#!/usr/bin/env python3
"""
Comprehensive Test Suite for TCP String Search Server

This module contains extensive unit tests for all components of the
TCP String Search Server, ensuring robust functionality and error handling.

Test Coverage:
- ServerConfig: Configuration loading, validation, and error handling
- FileSearchEngine: Search functionality with different modes
- StringSearchHandler: TCP request handling and protocol compliance
- StringSearchServer: Server initialization and socket management
- SSL Context: SSL/TLS configuration and certificate handling
- Logging: Log configuration and directory creation

The tests use mocking extensively to isolate components and test
edge cases without requiring external dependencies or file system
modifications during testing.

Author: <PERSON>
Date: 2025
"""

from server import (
    ServerConfig, FileSearchEngine, setup_logging, StringSearchHandler,
    StringSearchServer, create_ssl_context, ConfigurationError, FileSearchError
)
import os
import sys
import tempfile
import configparser
import ssl
import socket
import threading
import time
import logging
from pathlib import Path
from unittest import mock
from unittest.mock import patch, MagicMock

import pytest

CONFIG_PATH = os.path.join(os.path.dirname(
    os.path.dirname(__file__)), "config.ini")


@pytest.fixture
def real_config():
    return ServerConfig(CONFIG_PATH)


class TestServerConfig:
    """Test cases for ServerConfig class."""

    def test_config_file_not_found(self) -> None:
        """Test ConfigurationError when config file doesn't exist."""
        with pytest.raises(ConfigurationError, match="Configuration file.*not found"):
            ServerConfig("nonexistent.ini")

    def test_config_parse_error(self) -> None:
        """Test handling of malformed configuration file."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.ini') as f:
            f.write("[INVALID\nmalformed config")
            config_path = f.name

        try:
            with pytest.raises(ConfigurationError, match="Failed to parse configuration file"):
                ServerConfig(config_path)
        finally:
            os.unlink(config_path)

    def test_config_section_exception_handling(self) -> None:
        """Test exception handling in config section access."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.ini') as f:
            f.write("""[DEFAULT]
linuxpath = test_data/200k.txt
reread_on_query = False
host = localhost
port = 8888
max_connections = 100

[invalid_section]
invalid_key = value
""")
            config_path = f.name

        try:
            config = ServerConfig(config_path)
            # This should work fine and not trigger the exception path
            assert config.get("linuxpath") == "test_data/200k.txt"
        finally:
            os.unlink(config_path)

    def test_config_type_validation_errors(self) -> None:
        """Test type validation errors for configuration parameters."""
        # Test with a config that has invalid boolean value that will trigger ValueError
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.ini') as f:
            f.write("""[DEFAULT]
linuxpath = test_data/200k.txt
reread_on_query = invalid_boolean
host = localhost
port = 8888
max_connections = 100
""")
            config_path = f.name

        try:
            # Mock the ServerConfig's getboolean method to raise ValueError during validation
            with patch.object(ServerConfig, 'getboolean') as mock_getboolean:
                mock_getboolean.side_effect = ValueError("Invalid boolean value")
                with pytest.raises(ConfigurationError, match="Invalid type for parameter 'reread_on_query'"):
                    ServerConfig(config_path)
        finally:
            os.unlink(config_path)

    def test_file_not_readable(self) -> None:
        """Test handling of unreadable file."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("test content")
            test_file = f.name

        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.ini') as f:
            f.write(f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = False
host = localhost
port = 8888
max_connections = 100
""")
            config_path = f.name

        try:
            # Make file unreadable
            os.chmod(test_file, 0o000)
            with pytest.raises(ConfigurationError, match="File not readable"):
                ServerConfig(config_path)
        finally:
            # Restore permissions and cleanup
            os.chmod(test_file, 0o644)
            os.unlink(test_file)
            os.unlink(config_path)

    def test_config_required_parameter_missing(self) -> None:
        """Test handling of missing required parameter."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.ini') as f:
            f.write("""[DEFAULT]
# Missing linuxpath parameter
reread_on_query = False
host = localhost
port = 8888
max_connections = 100
""")
            config_path = f.name

        try:
            with pytest.raises(ConfigurationError, match="Required parameter 'linuxpath' not found"):
                ServerConfig(config_path)
        finally:
            os.unlink(config_path)

    def test_config_get_with_section_exception(self) -> None:
        """Test exception handling in get method with section."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.ini') as f:
            f.write("""[DEFAULT]
linuxpath = test_data/200k.txt
reread_on_query = False
host = localhost
port = 8888
max_connections = 100
""")
            config_path = f.name

        try:
            config = ServerConfig(config_path)
            # Test get with non-existent section
            result = config.get("nonexistent_key", "default_value", section="nonexistent_section")
            assert result == "default_value"
        finally:
            os.unlink(config_path)

    def test_valid_configuration(self) -> None:
        """Test successful configuration loading from real config.ini."""
        config = ServerConfig(CONFIG_PATH)
        assert config.get("linuxpath")
        assert isinstance(config.getboolean("reread_on_query"), bool)
        assert config.get("host")
        assert isinstance(config.getint("port"), int)
        assert isinstance(config.getint("max_connections"), int)

    def test_get_methods(self) -> None:
        """Test get, getboolean, and getint methods from real config.ini."""
        config = ServerConfig(CONFIG_PATH)
        # Test get method
        assert config.get("host")
        assert config.get("nonexistent", "default") == "default"
        # Test getboolean method
        assert isinstance(config.getboolean("reread_on_query"), bool)
        assert config.getboolean("nonexistent", True) is True
        # Test getint method
        assert isinstance(config.getint("port"), int)
        assert config.getint("nonexistent", 99) == 99


class TestFileSearchEngine:
    """Test cases for FileSearchEngine class."""

    def test_file_not_found(self) -> None:
        with pytest.raises(FileSearchError, match="File not found"):
            FileSearchEngine("/nonexistent/file.txt")

    def test_search_engine_initialization(self) -> None:
        config = ServerConfig("config.ini")
        engine = FileSearchEngine(config.file_path, False)
        assert engine.file_path == config.file_path
        assert engine.reread_on_query is False
        engine_reread = FileSearchEngine(config.file_path, True)
        assert engine_reread.reread_on_query is True

    @patch('search_algorithms_new.HashSetSearch')
    def test_search_without_reread(self, mock_search_class: MagicMock) -> None:
        config = ServerConfig("config.ini")
        mock_searcher = MagicMock()
        mock_search_class.return_value = mock_searcher
        mock_searcher.search.return_value = True
        engine = FileSearchEngine(config.file_path, False)
        result = engine.search("test query")
        assert result is True
        mock_searcher.load.assert_called_once_with(config.file_path)
        mock_searcher.search.assert_called_once_with("test query")

    @patch('search_algorithms_new.HashSetSearch')
    def test_search_with_reread(self, mock_search_class: MagicMock) -> None:
        config = ServerConfig("config.ini")
        mock_searcher = MagicMock()
        mock_search_class.return_value = mock_searcher
        mock_searcher.search.return_value = False
        engine = FileSearchEngine(config.file_path, True)
        result = engine.search("test query")
        assert result is False
        mock_searcher.load.assert_not_called()
        mock_searcher.search.assert_called_once_with(
            "test query", config.file_path)

    @patch('search_algorithms_new.HashSetSearch')
    def test_search_exception_handling(self, mock_search_class: MagicMock) -> None:
        config = ServerConfig("config.ini")
        mock_searcher = MagicMock()
        mock_search_class.return_value = mock_searcher
        mock_searcher.search.side_effect = Exception("Search failed")
        engine = FileSearchEngine(config.file_path, False)
        with pytest.raises(FileSearchError, match="Search failed"):
            engine.search("test query")


class TestSetupLogging:
    """Test cases for setup_logging function."""

    @patch('logging.basicConfig')
    @patch('os.makedirs')
    def test_setup_logging_with_log_dir(self, mock_makedirs: MagicMock, mock_basicConfig: MagicMock) -> None:
        """Test setup_logging creates log directory."""
        setup_logging("INFO", "logs/test.log")

        mock_makedirs.assert_called_once_with("logs", exist_ok=True)
        mock_basicConfig.assert_called_once()

    @patch('logging.basicConfig')
    def test_setup_logging_without_log_dir(self, mock_basicConfig: MagicMock) -> None:
        """Test setup_logging without log directory."""
        setup_logging("DEBUG", "test.log")

        mock_basicConfig.assert_called_once()
        args, kwargs = mock_basicConfig.call_args
        assert kwargs['level'] == logging.DEBUG

    @patch('logging.basicConfig')
    def test_setup_logging_default_file(self, mock_basicConfig: MagicMock) -> None:
        """Test setup_logging with default log file."""
        setup_logging("WARNING", "")

        mock_basicConfig.assert_called_once()


class TestStringSearchHandler:
    """Test cases for StringSearchHandler class."""

    def create_mock_handler(self, data: bytes, config: dict = None) -> StringSearchHandler:
        """Create a mock handler for testing."""
        handler = StringSearchHandler(None, None, None)
        handler.request = MagicMock()
        handler.request.recv.return_value = data
        handler.client_address = ("127.0.0.1", 12345)

        # Mock server with config
        handler.server = MagicMock()
        mock_config = MagicMock()
        if config:
            mock_config.getint.side_effect = lambda key, default, section=None: config.get(
                key, default)
        else:
            mock_config.getint.return_value = 1024
        handler.server.config = mock_config

        # Mock search engine
        handler.server.search_engine = MagicMock()
        handler.server.search_engine.search.return_value = True

        return handler

    def test_handler_empty_request(self) -> None:
        """Test handler with empty request."""
        # Create handler without calling constructor to avoid immediate handle() call
        handler = StringSearchHandler.__new__(StringSearchHandler)
        handler.request = MagicMock()
        handler.request.recv.return_value = b""
        handler.client_address = ("127.0.0.1", 12345)

        # Mock server with config
        handler.server = MagicMock()
        mock_config = MagicMock()
        mock_config.getint.return_value = 1024
        handler.server.config = mock_config

        with patch('server.logging') as mock_logging:
            handler.handle()
            mock_logging.warning.assert_called_with("Empty request from 127.0.0.1")

    def test_handler_empty_query_after_processing(self) -> None:
        """Test handler with query that becomes empty after processing."""
        # Create handler without calling constructor to avoid immediate handle() call
        handler = StringSearchHandler.__new__(StringSearchHandler)
        handler.request = MagicMock()
        handler.request.recv.return_value = b"\x00\x00\x00"  # Only null characters
        handler.client_address = ("127.0.0.1", 12345)

        # Mock server with config
        handler.server = MagicMock()
        mock_config = MagicMock()
        mock_config.getint.return_value = 1024
        handler.server.config = mock_config

        with patch('server.logging') as mock_logging:
            handler.handle()
            mock_logging.warning.assert_called_with("Empty query after processing from 127.0.0.1")
            handler.request.sendall.assert_called_with(b"STRING NOT FOUND\n")

    def test_handler_no_server_config(self) -> None:
        """Test handler when server has no config attribute."""
        # Create handler without calling constructor to avoid immediate handle() call
        handler = StringSearchHandler.__new__(StringSearchHandler)
        handler.request = MagicMock()
        handler.request.recv.return_value = b"test_query"
        handler.client_address = ("127.0.0.1", 12345)

        # Server with no config attribute
        handler.server = MagicMock()
        del handler.server.config  # Remove config attribute

        # Mock search engine
        mock_search_engine = MagicMock()
        mock_search_engine.search.return_value = True
        handler.server.search_engine = mock_search_engine

        with patch('server.logging'):
            handler.handle()
            # Should use default max_payload of 1024
            handler.request.recv.assert_called_with(1024)


class TestStringSearchServer:
    """Test cases for StringSearchServer class."""

    def create_mock_config(self, **kwargs) -> MagicMock:
        """Create a mock configuration."""
        config = MagicMock()
        defaults = {
            "socket_buffer_size": 262144,
            "tcp_nodelay": True,
            "connection_timeout": 5
        }
        defaults.update(kwargs)

        config.getint.side_effect = lambda key, default, section=None: defaults.get(
            key, default)
        config.getboolean.side_effect = lambda key, default, section=None: defaults.get(
            key, default)
        return config

    def create_mock_search_engine(self, file_path: str = "/test/file.txt", reread: bool = False) -> MagicMock:
        """Create a mock search engine."""
        engine = MagicMock()
        engine.file_path = file_path
        engine.reread_on_query = reread
        return engine

    @patch('server.logging')
    def test_server_initialization(self, mock_logging: MagicMock) -> None:
        """Test server initialization."""
        config = self.create_mock_config()
        search_engine = self.create_mock_search_engine()

        with patch('socket.socket') as mock_socket:
            mock_socket_instance = MagicMock()
            mock_socket.return_value = mock_socket_instance

            server = StringSearchServer(
                ("localhost", 8888), config, search_engine)

            assert server.config == config
            assert server.search_engine == search_engine
            mock_logging.info.assert_called()

    def test_server_socket_options(self) -> None:
        """Test server socket options configuration."""
        config = self.create_mock_config()
        search_engine = self.create_mock_search_engine()

        with patch('socket.socket') as mock_socket:
            mock_socket_instance = MagicMock()
            mock_socket.return_value = mock_socket_instance

            server = StringSearchServer(
                ("localhost", 8888), config, search_engine)

            # Verify socket options were set
            calls = mock_socket_instance.setsockopt.call_args_list
            # At least SO_REUSEADDR, SO_RCVBUF, SO_SNDBUF, TCP_NODELAY
            assert len(calls) >= 4

    def test_server_ipv6_binding(self) -> None:
        """Test IPv6 socket binding."""
        config = self.create_mock_config()
        search_engine = self.create_mock_search_engine()

        with patch('socket.socket') as mock_socket:
            mock_socket_instance = MagicMock()
            mock_socket.return_value = mock_socket_instance

            server = StringSearchServer(("::1", 8888), config, search_engine)
            server.server_bind()

            # Verify IPv6 options were set
            mock_socket_instance.setsockopt.assert_any_call(
                socket.IPPROTO_IPV6, socket.IPV6_V6ONLY, 0
            )

    @patch('server.logging')
    def test_handle_error(self, mock_logging: MagicMock) -> None:
        """Test error handling."""
        config = self.create_mock_config()
        search_engine = self.create_mock_search_engine()

        with patch('socket.socket'):
            server = StringSearchServer(
                ("localhost", 8888), config, search_engine)
            server.handle_error(None, ("127.0.0.1", 12345))

            mock_logging.error.assert_called_once()

    def test_shutdown_request(self) -> None:
        """Test request shutdown."""
        config = self.create_mock_config()
        search_engine = self.create_mock_search_engine()

        with patch('socket.socket'):
            server = StringSearchServer(
                ("localhost", 8888), config, search_engine)

            mock_request = MagicMock()
            with patch.object(server.__class__.__bases__[0], 'shutdown_request') as mock_super:
                server.shutdown_request(mock_request)

                mock_request.shutdown.assert_called_once_with(socket.SHUT_RDWR)
                mock_super.assert_called_once_with(mock_request)


class TestCreateSSLContext:
    """Test cases for create_ssl_context function."""

    def create_mock_config(self, ssl_enabled: bool = True, **kwargs) -> MagicMock:
        """Create a mock configuration for SSL testing."""
        config = MagicMock()
        defaults = {
            "ssl_enabled": ssl_enabled,
            "ssl_cert_file": "certs/server.crt",
            "ssl_key_file": "certs/server.key",
            "verify_client_cert": False,
            "min_tls_version": "TLSv1.3",
            "ca_file": None
        }
        defaults.update(kwargs)

        config.getboolean.side_effect = lambda key, default, section=None: defaults.get(
            key, default)
        config.get.side_effect = lambda key, default, section=None: defaults.get(
            key, default)
        return config

    def test_ssl_disabled(self) -> None:
        """Test SSL context when SSL is disabled."""
        config = self.create_mock_config(ssl_enabled=False)
        result = create_ssl_context(config)
        assert result is None

    def test_ssl_cert_file_not_found(self) -> None:
        """Test SSL context when certificate file is missing."""
        config = self.create_mock_config(ssl_cert_file="/nonexistent/cert.crt")

        with pytest.raises(FileSearchError, match="SSL certificate file not found"):
            create_ssl_context(config)

    def test_ssl_key_file_not_found(self, tmp_path: Path) -> None:
        """Test SSL context when key file is missing."""
        cert_file = tmp_path / "server.crt"
        cert_file.write_text("dummy cert")

        config = self.create_mock_config(
            ssl_cert_file=str(cert_file),
            ssl_key_file="/nonexistent/key.key"
        )

        with pytest.raises(FileSearchError, match="SSL key file not found"):
            create_ssl_context(config)

    @patch('ssl.create_default_context')
    @patch('os.path.exists', return_value=True)
    def test_ssl_context_creation_success(self, mock_exists: MagicMock, mock_create_context: MagicMock) -> None:
        """Test successful SSL context creation."""
        mock_context = MagicMock()
        mock_create_context.return_value = mock_context

        config = self.create_mock_config()
        result = create_ssl_context(config)

        assert result == mock_context
        mock_context.load_cert_chain.assert_called_once()
        mock_context.set_ciphers.assert_called_once()

    @patch('ssl.create_default_context')
    @patch('os.path.exists', return_value=True)
    def test_ssl_context_tls_versions(self, mock_exists: MagicMock, mock_create_context: MagicMock) -> None:
        """Test SSL context with different TLS versions."""
        mock_context = MagicMock()
        mock_create_context.return_value = mock_context

        # Test TLSv1.3
        config = self.create_mock_config(min_tls_version="TLSv1.3")
        create_ssl_context(config)
        assert mock_context.minimum_version == ssl.TLSVersion.TLSv1_3

        # Test TLSv1.2
        config = self.create_mock_config(min_tls_version="TLSv1.2")
        create_ssl_context(config)
        assert mock_context.minimum_version == ssl.TLSVersion.TLSv1_2

    @patch('ssl.create_default_context')
    @patch('os.path.exists', return_value=True)
    @patch('server.logging')
    def test_ssl_context_client_verification(self, mock_logging: MagicMock, mock_exists: MagicMock, mock_create_context: MagicMock) -> None:
        """Test SSL context with client certificate verification."""
        mock_context = MagicMock()
        mock_create_context.return_value = mock_context

        config = self.create_mock_config(
            verify_client_cert=True, ca_file="ca.crt")
        create_ssl_context(config)

        assert mock_context.verify_mode == ssl.CERT_REQUIRED
        mock_context.load_verify_locations.assert_called_once_with("ca.crt")

    @patch('ssl.create_default_context')
    @patch('os.path.exists', return_value=True)
    def test_ssl_context_creation_failure(self, mock_exists: MagicMock, mock_create_context: MagicMock) -> None:
        """Test SSL context creation failure."""
        mock_create_context.side_effect = Exception("SSL error")

        config = self.create_mock_config()

        with pytest.raises(FileSearchError, match="Failed to create SSL context"):
            create_ssl_context(config)


class TestIntegration:
    """Integration tests for the complete server system."""

    @pytest.fixture
    def temp_config_and_file(self, tmp_path: Path) -> tuple[Path, Path]:
        """Create temporary config file and search file."""
        # Create search file
        search_file = tmp_path / "search.txt"
        search_file.write_text("line1\nline2\ntest line\nanother line\n")

        # Create config file
        config_file = tmp_path / "config.ini"
        config_file.write_text(f"""
[DEFAULT]
linuxpath = {search_file}
REREAD_ON_QUERY = false
host = localhost
port = 0
max_connections = 10

[server]
host = localhost
port = 0
""")

        return config_file, search_file

    def test_full_server_lifecycle(self, temp_config_and_file: tuple[Path, Path]) -> None:
        """Test complete server lifecycle with real socket connections."""
        config_file, search_file = temp_config_and_file

        # Create server components
        config = ServerConfig(str(config_file))
        search_engine = FileSearchEngine(str(search_file), False)

        # Start server on random port
        server = StringSearchServer(("localhost", 0), config, search_engine)
        actual_port = server.server_address[1]

        # Start server in thread
        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()

        try:
            # Wait for server to start
            time.sleep(0.1)

            # Test existing line
            with socket.create_connection(("localhost", actual_port)) as sock:
                sock.sendall(b"test line\n")
                response = sock.recv(1024).decode('utf-8')
                assert response == "STRING EXISTS\n"

            # Test non-existing line
            with socket.create_connection(("localhost", actual_port)) as sock:
                sock.sendall(b"nonexistent line\n")
                response = sock.recv(1024).decode('utf-8')
                assert response == "STRING NOT FOUND\n"

            # Test multiple concurrent connections
            def test_connection(query: str, expected: str) -> None:
                with socket.create_connection(("localhost", actual_port)) as sock:
                    sock.sendall(query.encode() + b"\n")
                    response = sock.recv(1024).decode('utf-8')
                    assert response == expected

            threads = []
            test_cases = [
                ("line1", "STRING EXISTS\n"),
                ("line2", "STRING EXISTS\n"),
                ("nonexistent", "STRING NOT FOUND\n"),
                ("another line", "STRING EXISTS\n"),
            ]

            for query, expected in test_cases:
                thread = threading.Thread(
                    target=test_connection, args=(query, expected))
                thread.start()
                threads.append(thread)

            for thread in threads:
                thread.join()

        finally:
            server.shutdown()
            server.server_close()
            server_thread.join(timeout=1)

    def test_reread_on_query_mode(self, temp_config_and_file: tuple[Path, Path]) -> None:
        """Test server with REREAD_ON_QUERY enabled."""
        config_file, search_file = temp_config_and_file

        # Modify config to enable reread
        config_content = config_file.read_text()
        config_content = config_content.replace(
            "REREAD_ON_QUERY = false", "REREAD_ON_QUERY = true")
        config_file.write_text(config_content)

        config = ServerConfig(str(config_file))
        search_engine = FileSearchEngine(str(search_file), True)

        server = StringSearchServer(("localhost", 0), config, search_engine)
        actual_port = server.server_address[1]

        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()

        try:
            time.sleep(0.1)

            # Test initial search
            with socket.create_connection(("localhost", actual_port)) as sock:
                sock.sendall(b"test line\n")
                response = sock.recv(1024).decode('utf-8')
                assert response == "STRING EXISTS\n"

            # Modify file content
            search_file.write_text("new line1\nnew line2\n")

            # Test that new content is found (due to reread)
            with socket.create_connection(("localhost", actual_port)) as sock:
                sock.sendall(b"new line1\n")
                response = sock.recv(1024).decode('utf-8')
                assert response == "STRING EXISTS\n"

            # Test that old content is no longer found
            with socket.create_connection(("localhost", actual_port)) as sock:
                sock.sendall(b"test line\n")
                response = sock.recv(1024).decode('utf-8')
                assert response == "STRING NOT FOUND\n"

        finally:
            server.shutdown()
            server.server_close()
            server_thread.join(timeout=1)


class TestErrorHandling:
    """Test cases for error handling scenarios."""

    def test_server_bind_error(self, tmp_path: Path) -> None:
        """Test server binding error handling."""
        config_file, search_file = self.create_temp_files(tmp_path)

        config = ServerConfig(str(config_file))
        search_engine = FileSearchEngine(str(search_file), False)

        # Try to bind to a privileged port (should fail without root)
        with pytest.raises(PermissionError):
            server = StringSearchServer(
                ("localhost", 80), config, search_engine)

    def test_socket_timeout_handling(self, tmp_path: Path) -> None:
        """Test socket timeout configuration."""
        config_file, search_file = self.create_temp_files(tmp_path)

        # Add timeout configuration
        config_content = config_file.read_text()
        config_content += "\nconnection_timeout = 1\n"
        config_file.write_text(config_content)

        config = ServerConfig(str(config_file))
        search_engine = FileSearchEngine(str(search_file), False)

        server = StringSearchServer(("localhost", 0), config, search_engine)

        # Verify timeout was set
        assert server.socket.gettimeout() == 1

        server.server_close()

    def test_large_payload_handling(self, tmp_path: Path) -> None:
        """Test handling of large payloads."""
        config_file, search_file = self.create_temp_files(tmp_path)

        # Set small max payload size
        config_content = config_file.read_text()
        config_content += "\n[server]\nmax_payload_size = 10\n"
        config_file.write_text(config_content)

        config = ServerConfig(str(config_file))
        search_engine = FileSearchEngine(str(search_file), False)

        server = StringSearchServer(("localhost", 0), config, search_engine)
        actual_port = server.server_address[1]

        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()

        try:
            time.sleep(0.1)

            # Send large payload
            with socket.create_connection(("localhost", actual_port)) as sock:
                large_query = "a" * 100  # Larger than max_payload_size
                sock.sendall(large_query.encode() + b"\n")
                response = sock.recv(1024).decode('utf-8')
                # Should still get a response, but truncated
                assert response in ["STRING EXISTS\n", "STRING NOT FOUND\n"]

        finally:
            server.shutdown()
            server.server_close()
            server_thread.join(timeout=1)

    def create_temp_files(self, tmp_path: Path) -> tuple[Path, Path]:
        """Helper method to create temporary files."""
        search_file = tmp_path / "search.txt"
        search_file.write_text("line1\nline2\ntest line\n")

        config_file = tmp_path / "config.ini"
        config_file.write_text(f"""
[DEFAULT]
linuxpath = {search_file}
REREAD_ON_QUERY = false
host = localhost
port = 0
max_connections = 10
""")

        return config_file, search_file


class TestCommandLineArguments:
    """Test cases for command line argument handling."""




class TestLoggingConfiguration:
    """Test cases for logging configuration."""

    @patch('logging.basicConfig')
    @patch('os.makedirs')
    def test_logging_levels(self, mock_makedirs: MagicMock, mock_basicConfig: MagicMock) -> None:
        """Test different logging levels."""
        levels = ["DEBUG", "INFO", "WARNING", "ERROR"]

        for level in levels:
            setup_logging(level, f"logs/{level.lower()}.log")

            # Verify basicConfig was called with correct level
            args, kwargs = mock_basicConfig.call_args
            expected_level = getattr(logging, level)
            assert kwargs['level'] == expected_level

    @patch('logging.basicConfig')
    def test_logging_format(self, mock_basicConfig: MagicMock) -> None:
        """Test logging format configuration."""
        setup_logging("INFO", "test.log")

        args, kwargs = mock_basicConfig.call_args
        expected_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        assert kwargs['format'] == expected_format

    @patch('logging.basicConfig')
    def test_logging_handlers(self, mock_basicConfig: MagicMock) -> None:
        """Test logging handlers configuration."""
        setup_logging("DEBUG", "logs/test.log")

        args, kwargs = mock_basicConfig.call_args
        handlers = kwargs['handlers']
        assert len(handlers) == 2  # FileHandler and StreamHandler


class TestPerformanceOptimizations:
    """Test cases for performance optimizations."""

    def test_socket_buffer_configuration(self, tmp_path: Path) -> None:
        """Test socket buffer size configuration."""
        config_file, search_file = self.create_temp_files(tmp_path)

        # Add socket buffer configuration
        config_content = config_file.read_text()
        config_content += "\nsocket_buffer_size = 512000\n"
        config_file.write_text(config_content)

        config = ServerConfig(str(config_file))
        search_engine = FileSearchEngine(str(search_file), False)

        with patch('socket.socket') as mock_socket:
            mock_socket_instance = MagicMock()
            mock_socket.return_value = mock_socket_instance

            server = StringSearchServer(
                ("localhost", 0), config, search_engine)

            # Verify buffer size was set
            mock_socket_instance.setsockopt.assert_any_call(
                socket.SOL_SOCKET, socket.SO_RCVBUF, 512000
            )
            mock_socket_instance.setsockopt.assert_any_call(
                socket.SOL_SOCKET, socket.SO_SNDBUF, 512000
            )

    def test_tcp_nodelay_configuration(self, tmp_path: Path) -> None:
        """Test TCP_NODELAY configuration."""
        config_file, search_file = self.create_temp_files(tmp_path)

        # Add TCP_NODELAY configuration
        config_content = config_file.read_text()
        config_content += "\ntcp_nodelay = true\n"
        config_file.write_text(config_content)

        config = ServerConfig(str(config_file))
        search_engine = FileSearchEngine(str(search_file), False)

        with patch('socket.socket') as mock_socket:
            mock_socket_instance = MagicMock()
            mock_socket.return_value = mock_socket_instance

            server = StringSearchServer(
                ("localhost", 0), config, search_engine)

            # Verify TCP_NODELAY was set
            mock_socket_instance.setsockopt.assert_any_call(
                socket.IPPROTO_TCP, socket.TCP_NODELAY, 1
            )

    def test_keepalive_configuration(self, tmp_path: Path) -> None:
        """Test socket keepalive configuration."""
        config_file, search_file = self.create_temp_files(tmp_path)

        config = ServerConfig(str(config_file))
        search_engine = FileSearchEngine(str(search_file), False)

        with patch('socket.socket') as mock_socket:
            mock_socket_instance = MagicMock()
            mock_socket.return_value = mock_socket_instance

            server = StringSearchServer(
                ("localhost", 0), config, search_engine)

            # Verify keepalive was set
            mock_socket_instance.setsockopt.assert_any_call(
                socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1
            )

    def create_temp_files(self, tmp_path: Path) -> tuple[Path, Path]:
        """Helper method to create temporary files."""
        search_file = tmp_path / "search.txt"
        search_file.write_text("test content")

        config_file = tmp_path / "config.ini"
        config_file.write_text(f"""
[DEFAULT]
linuxpath = {search_file}
REREAD_ON_QUERY = false
host = localhost
port = 0
max_connections = 10
""")

        return config_file, search_file


class TestThreadingBehavior:
    """Test cases for threading behavior."""

    def test_concurrent_request_handling(self, tmp_path: Path) -> None:
        """Test concurrent request handling."""
        config_file, search_file = self.create_temp_files(tmp_path)

        config = ServerConfig(str(config_file))
        search_engine = FileSearchEngine(str(search_file), False)

        server = StringSearchServer(("localhost", 0), config, search_engine)
        actual_port = server.server_address[1]

        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()

        try:
            time.sleep(0.1)

            # Create multiple concurrent connections
            results = []

            def make_request(query: str) -> None:
                with socket.create_connection(("localhost", actual_port)) as sock:
                    sock.sendall(query.encode() + b"\n")
                    response = sock.recv(1024).decode('utf-8')
                    results.append(response)

            # Start multiple threads simultaneously
            threads = []
            for i in range(20):
                thread = threading.Thread(
                    target=make_request, args=(f"test{i}",))
                threads.append(thread)

            # Start all threads
            for thread in threads:
                thread.start()

            # Wait for all threads to complete
            for thread in threads:
                thread.join()

            # Verify all requests were handled
            assert len(results) == 20
            for result in results:
                assert result in ["STRING EXISTS\n", "STRING NOT FOUND\n"]

        finally:
            server.shutdown()
            server.server_close()
            server_thread.join(timeout=1)

    def test_thread_safety(self, tmp_path: Path) -> None:
        """Test thread safety of search engine."""
        config_file, search_file = self.create_temp_files(tmp_path)

        config = ServerConfig(str(config_file))
        search_engine = FileSearchEngine(str(search_file), False)

        # Test that the same search engine can be used concurrently
        results = []

        def search_worker(query: str) -> None:
            try:
                result = search_engine.search(query)
                results.append(result)
            except Exception as e:
                results.append(f"Error: {e}")

        threads = []
        for i in range(10):
            thread = threading.Thread(
                target=search_worker, args=(f"query{i}",))
            threads.append(thread)

        for thread in threads:
            thread.start()

        for thread in threads:
            thread.join()

        # Verify all searches completed without errors
        assert len(results) == 10
        for result in results:
            assert isinstance(result, bool)

    def create_temp_files(self, tmp_path: Path) -> tuple[Path, Path]:
        """Helper method to create temporary files."""
        search_file = tmp_path / "search.txt"
        search_file.write_text("line1\nline2\ntest content\n")

        config_file = tmp_path / "config.ini"
        config_file.write_text(f"""
[DEFAULT]
linuxpath = {search_file}
REREAD_ON_QUERY = false
host = localhost
port = 0
max_connections = 10
""")

        return config_file, search_file


class TestEdgeCases:
    """Test cases for edge cases and boundary conditions."""

    def test_empty_search_file(self, tmp_path: Path) -> None:
        """Test handling of empty search file."""
        search_file = tmp_path / "empty.txt"
        search_file.write_text("")

        config_file = tmp_path / "config.ini"
        config_file.write_text(f"""
[DEFAULT]
linuxpath = {search_file}
REREAD_ON_QUERY = false
host = localhost
port = 0
max_connections = 10
""")

        config = ServerConfig(str(config_file))
        search_engine = FileSearchEngine(str(search_file), False)

        # Should not raise an exception
        result = search_engine.search("any query")
        assert result is False

    def test_very_long_query(self, tmp_path: Path) -> None:
        """Test handling of very long queries."""
        config_file, search_file = self.create_temp_files(tmp_path)

        config = ServerConfig(str(config_file))
        search_engine = FileSearchEngine(str(search_file), False)

        server = StringSearchServer(("localhost", 0), config, search_engine)
        actual_port = server.server_address[1]

        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()

        try:
            time.sleep(0.1)

            # Send very long query
            with socket.create_connection(("localhost", actual_port)) as sock:
                long_query = "a" * 10000
                sock.sendall(long_query.encode() + b"\n")
                response = sock.recv(1024).decode('utf-8')
                assert response in ["STRING EXISTS\n", "STRING NOT FOUND\n"]

        finally:
            server.shutdown()
            server.server_close()
            server_thread.join(timeout=1)

    def test_special_characters_in_query(self, tmp_path: Path) -> None:
        """Test handling of special characters in queries."""
        search_file = tmp_path / "special.txt"
        search_file.write_text("special!@#$%^&*()_+\nunicode: àáâãäå\n")

        config_file = tmp_path / "config.ini"
        config_file.write_text(f"""
[DEFAULT]
linuxpath = {search_file}
REREAD_ON_QUERY = false
host = localhost
port = 0
max_connections = 10
""")

        config = ServerConfig(str(config_file))
        search_engine = FileSearchEngine(str(search_file), False)

        server = StringSearchServer(("localhost", 0), config, search_engine)
        actual_port = server.server_address[1]

        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()

        try:
            time.sleep(0.1)

            # Test special characters
            with socket.create_connection(("localhost", actual_port)) as sock:
                sock.sendall("special!@#$%^&*()_+".encode('utf-8') + b"\n")
                response = sock.recv(1024).decode('utf-8')
                assert response == "STRING EXISTS\n"

            # Test unicode characters
            with socket.create_connection(("localhost", actual_port)) as sock:
                sock.sendall("unicode: àáâãäå".encode('utf-8') + b"\n")
                response = sock.recv(1024).decode('utf-8')
                assert response == "STRING EXISTS\n"

        finally:
            server.shutdown()
            server.server_close()
            server_thread.join(timeout=1)

    def test_binary_data_handling(self, tmp_path: Path) -> None:
        """Test handling of binary data in queries."""
        config_file, search_file = self.create_temp_files(tmp_path)

        config = ServerConfig(str(config_file))
        search_engine = FileSearchEngine(str(search_file), False)

        server = StringSearchServer(("localhost", 0), config, search_engine)
        actual_port = server.server_address[1]

        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()

        try:
            time.sleep(0.1)

            # Send binary data
            with socket.create_connection(("localhost", actual_port)) as sock:
                binary_data = b'\x00\x01\x02\x03\x04\x05'
                sock.sendall(binary_data + b"\n")
                response = sock.recv(1024).decode('utf-8')
                assert response in ["STRING EXISTS\n", "STRING NOT FOUND\n"]

        finally:
            server.shutdown()
            server.server_close()
            server_thread.join(timeout=1)

    def create_temp_files(self, tmp_path: Path) -> tuple[Path, Path]:
        """Helper method to create temporary files."""
        search_file = tmp_path / "search.txt"
        search_file.write_text("line1\nline2\ntest content\n")

        config_file = tmp_path / "config.ini"
        config_file.write_text(f"""
[DEFAULT]
linuxpath = {search_file}
REREAD_ON_QUERY = false
host = localhost
port = 0
max_connections = 10
""")

        return config_file, search_file


class TestMainFunction:
    """Test cases for the main function."""

    @patch('server.setup_logging')
    @patch('server.ServerConfig')
    @patch('server.FileSearchEngine')
    @patch('server.StringSearchServer')
    @patch('server.create_ssl_context')
    @patch('sys.argv', ['server.py'])
    def test_main_function_normal_execution(self, mock_ssl_context, mock_server_class,
                                          mock_search_engine_class, mock_config_class, mock_setup_logging):
        """Test main function normal execution."""
        # Mock configuration
        mock_config = MagicMock()
        mock_config.config_file = "config.ini"
        mock_config.file_path = "test_data/200k.txt"
        mock_config.getboolean.return_value = False
        mock_config.get.side_effect = lambda key, default: {"host": "localhost"}.get(key, default)
        mock_config.getint.side_effect = lambda key, default: {"port": 8888}.get(key, default)
        mock_config_class.return_value = mock_config

        # Mock search engine
        mock_search_engine = MagicMock()
        mock_search_engine_class.return_value = mock_search_engine

        # Mock server
        mock_server = MagicMock()
        mock_server_class.return_value = mock_server

        # Mock SSL context (disabled)
        mock_ssl_context.return_value = None

        # Mock server.serve_forever to raise KeyboardInterrupt
        mock_server.serve_forever.side_effect = KeyboardInterrupt()

        with patch('server.logging') as mock_logging:
            from server import main
            main()

        # Verify setup_logging was called
        mock_setup_logging.assert_called_once_with("DEBUG", "logs/server.log")

        # Verify server was created and started
        mock_server_class.assert_called_once_with(("localhost", 8888), mock_config, mock_search_engine)
        mock_server.serve_forever.assert_called_once()
        mock_server.shutdown.assert_called_once()
        mock_server.server_close.assert_called_once()

    @patch('server.setup_logging')
    @patch('server.ServerConfig')
    @patch('sys.argv', ['server.py'])
    def test_main_function_startup_exception(self, mock_config_class, mock_setup_logging):
        """Test main function with startup exception."""
        # Mock configuration to raise exception
        mock_config_class.side_effect = Exception("Configuration error")

        with patch('server.logging') as mock_logging:
            from server import main
            main()

        # Verify error was logged
        mock_logging.error.assert_called_with("Server startup failed: Configuration error")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
