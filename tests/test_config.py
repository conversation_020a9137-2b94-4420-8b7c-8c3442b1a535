#!/usr/bin/env python3
"""Tests for ServerConfig class."""

import pytest
from server import ServerConfig, ConfigurationError

def test_config_loading():
    """Test basic configuration loading."""
    config = ServerConfig()
    assert config.file_path is not None
    assert isinstance(config.get('host', section='server'), str)
    assert isinstance(config.getint('port', section='server'), int)
    assert isinstance(config.getboolean('REREAD_ON_QUERY', section='DEFAULT'), bool)
    assert isinstance(config.get('ssl_enabled', section='ssl'), str)
    assert isinstance(config.get('log_level', section='logging'), str)

def test_invalid_config_file():
    """Test handling of invalid config file."""
    with pytest.raises(ConfigurationError):
        ServerConfig("nonexistent.ini")

def test_required_parameters():
    """Test validation of required parameters."""
    config = ServerConfig()
    # Test required parameters from DEFAULT section
    assert config.get('linuxpath') is not None
    assert isinstance(config.getboolean('REREAD_ON_QUERY'), bool)
    
    # Test required parameters from server section
    assert config.get('host', section='server') is not None
    assert config.getint('port', section='server') > 0
    assert config.getint('max_connections', section='server') > 0
