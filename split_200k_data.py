#!/usr/bin/env python3
"""
Benchmark Data File Splitter

This utility splits a large source file (200k.txt) into smaller files
of various sizes for performance benchmarking of search algorithms.
Creates standardized test files with 10K, 50K, 100K, and 250K lines.

The generated files are used by the performance testing suite to
evaluate search algorithm performance across different file sizes.

Author: <PERSON>
Date: 2025
"""
import os
import logging


def split_file(source_file: str, output_sizes: list[tuple[int, str]]) -> None:
    """
    Split a source file into multiple smaller files for benchmarking.

    Reads the entire source file and creates multiple output files,
    each containing the first N lines from the source file where N
    is specified in the output_sizes parameter.

    Args:
        source_file: Path to the source file to split
        output_sizes: List of tuples containing (line_count, output_filename)
                     Each tuple specifies how many lines to include and
                     what to name the output file

    Raises:
        FileNotFoundError: If source file doesn't exist
        PermissionError: If unable to read source or write output files
        OSError: If unable to create output directory

    Example:
        split_file('data.txt', [(1000, 'small.txt'), (5000, 'medium.txt')])
    """
    logging.info(f"Reading source file: {source_file}")

    # Read all lines from source file
    with open(source_file, 'r') as f:
        lines = f.readlines()

    total_lines = len(lines)
    logging.info(f"Total lines in source file: {total_lines:,}")

    # Create output directory if it doesn't exist
    os.makedirs('test_data', exist_ok=True)

    # Create files with different sizes
    for size, filename in output_sizes:
        output_path = os.path.join('test_data', filename)
        with open(output_path, 'w') as f:
            f.writelines(lines[:size])
        logging.info(f"Generated {output_path} with {size:,} lines")


def main() -> None:
    """
    Main entry point for the file splitter utility.

    Configures logging and splits the 200k.txt file into standard
    benchmark sizes: 10K, 50K, 100K, and 250K lines.

    The output files are created in the 'test_data' directory and
    are used by the performance testing suite.
    """
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    # Define output sizes and filenames
    sizes = [
        (10000, 'bench_10000.txt'),
        (50000, 'bench_50000.txt'),
        (100000, 'bench_100000.txt'),
        (250000, 'bench_250000.txt')
    ]

    source_file = 'test_data/200k.txt'
    split_file(source_file, sizes)


if __name__ == '__main__':
    main()
