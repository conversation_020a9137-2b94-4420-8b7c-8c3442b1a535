#!/usr/bin/env python3
"""
TCP String Search Client

A client for testing the TCP String Search Server.
Supports both SSL and non-SSL connections with performance measurement.

Author: Interview Candidate
Date: 2024
"""

import socket
import ssl
import time
import argparse
import sys
from typing import Tuple


class SearchClient:
    """
    TCP client for string search server communication.

    Supports both SSL and non-SSL connections with automatic
    performance measurement and error handling.
    """

    def __init__(self, host: str = 'localhost', port: int = 8888,
                 use_ssl: bool = False, timeout: float = 10.0,
                 cert_file: str | None = None, key_file: str | None = None,
                 ca_file: str | None = None, verify_cert: bool = True) -> None:
        """
        Initialize search client.

        Args:
            host: Server hostname or IP address
            port: Server port number
            use_ssl: Whether to use SSL connection
            timeout: Connection timeout in seconds
            cert_file: Path to client certificate file (optional)
            key_file: Path to client private key file (optional)
            ca_file: Path to CA certificate file for server verification (optional)
            verify_cert: Whether to verify server certificate (default True)
        """
        self.host = host
        self.port = port
        self.use_ssl = use_ssl
        self.timeout = timeout
        self.cert_file = cert_file
        self.key_file = key_file
        self.ca_file = ca_file
        self.verify_cert = verify_cert

    def create_ssl_context(self) -> ssl.SSLContext:
        """Create SSL context for server-side SSL only."""
        context = ssl.create_default_context()
        context.minimum_version = ssl.TLSVersion.TLSv1_3
        context.set_ciphers('ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384')
        if self.ca_file:
            context.load_verify_locations(self.ca_file)
            context.verify_mode = ssl.CERT_REQUIRED
            context.check_hostname = True
        elif not self.verify_cert:
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
        # Only load client cert if both are provided (for future use)
        if self.cert_file and self.key_file:
            context.load_cert_chain(self.cert_file, self.key_file)
        return context

    def search(self, query: str) -> Tuple[str, float]:
        """
        Send search query to server and return result with timing.

        Args:
            query: String to search for

        Returns:
            Tuple of (response, execution_time_ms)

        Raises:
            ConnectionError: If connection to server fails
            TimeoutError: If operation times out
        """
        start_time = time.time()
        sock = None

        try:
            # Create socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)

            # Wrap with SSL if required
            if self.use_ssl:
                context = self.create_ssl_context()
                sock = context.wrap_socket(sock, server_hostname=self.host)

            # Connect to server
            sock.connect((self.host, self.port))

            # Send query (ensure it's within 1024 bytes)
            query_bytes = query.encode('utf-8')
            if len(query_bytes) > 1024:
                raise ValueError(f"Query too long: {len(query_bytes)} bytes (max 1024)")

            sock.sendall(query_bytes)

            # Receive response
            response = sock.recv(1024).decode('utf-8').strip()

            # Calculate execution time
            execution_time = (time.time() - start_time) * 1000

            return response, execution_time

        except socket.timeout:
            raise TimeoutError(f"Connection to {self.host}:{self.port} timed out")
        except socket.error as e:
            raise ConnectionError(f"Failed to connect to {self.host}:{self.port}: {e}")
        finally:
            if sock is not None:
                sock.close()

    def test_connection(self) -> bool:
        """
        Test if server is reachable.

        Returns:
            True if server responds, False otherwise
        """
        try:
            _, _ = self.search("test_connection")
            return True
        except (ConnectionError, TimeoutError):
            return False


def main() -> None:
    """
    Main client entry point with command-line interface.
    """
    parser = argparse.ArgumentParser(description='TCP String Search Client')
    parser.add_argument('--host', default='localhost',
                        help='Server hostname (default: localhost)')
    parser.add_argument('--port', type=int, default=8888,
                        help='Server port (default: 8888)')
    parser.add_argument('--ssl', action='store_true',
                        help='Use SSL connection')
    parser.add_argument('--cert', type=str,
                        help='Path to client certificate file')
    parser.add_argument('--key', type=str,
                        help='Path to client private key file')
    parser.add_argument('--ca', type=str,
                        help='Path to CA certificate file for server verification')
    parser.add_argument('--no-verify', action='store_true',
                        help='Disable server certificate verification')
    parser.add_argument('--timeout', type=float, default=10.0,
                        help='Connection timeout in seconds (default: 10.0)')
    parser.add_argument('--query', type=str,
                        help='Single query to send')
    parser.add_argument('--interactive', action='store_true',
                        help='Interactive mode for multiple queries')
    parser.add_argument('--test', action='store_true',
                        help='Test connection to server')

    args = parser.parse_args()

    # Create client with SSL options if enabled
    client = SearchClient(
        host=args.host,
        port=args.port,
        use_ssl=args.ssl,
        timeout=args.timeout,
        cert_file=args.cert,
        key_file=args.key,
        ca_file=args.ca,
        verify_cert=not args.no_verify
    )

    # Test connection if requested
    if args.test:
        print(f"Testing connection to {args.host}:{args.port}...")
        if client.test_connection():
            print("✓ Server is reachable")
            return
        else:
            print("✗ Server is not reachable")
            sys.exit(1)

    # Single query mode
    if args.query:
        try:
            response, exec_time = client.search(args.query)
            print(f"Query: {args.query}")
            print(f"Response: {response}")
            print(f"Execution time: {exec_time:.2f}ms")
        except Exception as e:
            print(f"Error: {e}")
            sys.exit(1)
        return

    # Interactive mode
    if args.interactive:
        print(f"Connected to {args.host}:{args.port}")
        print("Enter queries (empty line to quit):")

        while True:
            try:
                query = input("> ").strip()
                if not query:
                    break

                response, exec_time = client.search(query)
                print(f"Response: {response}")
                print(f"Time: {exec_time:.2f}ms")

            except KeyboardInterrupt:
                print("\nGoodbye!")
                break
            except Exception as e:
                print(f"Error: {e}")
        return

    # Default: show help
    parser.print_help()


if __name__ == "__main__":
    main()
