# Search Algorithm Performance Report

## Performance Summary

### Average Search Time by File Size

| Algorithm                     |   10K |   50K |   100K |   250K |
|:------------------------------|------:|------:|-------:|-------:|
| Binary Search (Deduplicated)  | 0.002 | 0.003 |  0.003 |  0.002 |
| Hash Set (FrozenSet)          | 0.002 | 0.001 |  0.002 |  0.002 |
| Hash Set (FrozenSet) (Reread) | 0.032 | 0.017 |  0.015 |  0.023 |
| Linear Search (Optimized)     | 1.289 | 4.817 |  7.77  | 14.837 |
| Memory-Mapped                 | 1.814 | 7.414 |  9.161 | 18.129 |
| Native Grep                   | 2.638 | 3.665 |  2.93  |  4.102 |

### Load Time by File Size

| Algorithm                     |   10K |    50K |   100K |    250K |
|:------------------------------|------:|-------:|-------:|--------:|
| Binary Search (Deduplicated)  | 7.423 | 31.786 | 47.755 | 102.74  |
| Hash Set (FrozenSet)          | 3.786 | 18.346 | 38.186 | 123.606 |
| Hash Set (FrozenSet) (Reread) | 4.853 | 17.558 | 44.9   | 101.37  |
| Linear Search (Optimized)     | 0.003 |  0.001 |  0.001 |   0.001 |
| Memory-Mapped                 | 0.064 |  0.138 |  0.153 |   0.185 |
| Native Grep                   | 0.002 |  0.001 |  0.001 |   0.001 |

## Performance Visualizations

### Load Time Comparison
![Load Time Comparison](load_time_(ms)_chart.png)

### Search Time Comparison
![Search Time Comparison](avg_search_time_(ms)_chart.png)

## Algorithm Characteristics

1. **HashSet (FrozenSet)**: O(1) lookup with memory trade-off
2. **HashSet (FrozenSet) (Reread)**: No initial memory overhead, but slower search
3. **Linear Search**: Simple implementation, high time complexity (O(n))
4. **Binary Search**: O(log n) search with sorting overhead
5. **Memory-Mapped**: Efficient for large files
6. **Native Grep**: System-level optimization
